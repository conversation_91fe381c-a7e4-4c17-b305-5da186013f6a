<script setup lang="ts">
import type { PanelProps, DatePickerEmits } from './interface.ts'
// @ts-ignore
import { DatePanel, HeaderPanel, MonthPanel } from '@/components/common/DatePicker/panel'
import { useDatePickerPanel } from './DatePickerCore.ts'
const props = withDefaults(defineProps<PanelProps>(), {
  size: 'default',
})
const emits = defineEmits<DatePickerEmits>()

const modelValue = defineModel<number>('modalValue')

const panel = useDatePickerPanel(modelValue, emits)
</script>

<template>
  <div class="date-panel" :class="[{ 'date-panel-small': size === 'small' }]">
    <HeaderPanel v-bind="panel" />
    <DatePanel v-if="panel.panelMode === 'date'"
               v-bind="panel" />
    <MonthPanel
      v-else-if="panel.panelMode === 'month'"
      v-bind="panel"
    />
  </div>
</template>

<style scoped lang="less">
@width: 280px;
.date-panel {
  width: @width;
  color: var(--text-color);
}
.date-panel-small {
  @width: 255px;
  width: @width;
  overflow: hidden;
  .date-panel-weeks {
    grid-template-columns: repeat(7, calc((@width / 7 - 3px) * 1.06));
  }
  .date-panel-grid {
    grid-template-columns: repeat(7, calc(@width / 7 - 3px));
    grid-auto-rows: calc(@width / 7 - 6px);
  }
  .date-panel-cell {
    padding: 2px;
  }
  .date-panel-month-grid {
    padding: 4px;
  }
}
</style>
