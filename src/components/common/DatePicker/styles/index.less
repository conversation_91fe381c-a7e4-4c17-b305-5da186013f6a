@u-web-date-picker-width: 280px;

.u-web-date-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 0 6px 8px;
}
.u-web-date-panel-nav {
  display: flex;
  align-items: center;
  gap: 6px;
}
.u-web-date-panel-btn {
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  color: var(--color-neutral-6);
  &:hover {
    background: var(--color-fill-2);
  }
}

.u-web-date-panel-title {
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  &:hover {
    color: rgba(var(--blue-6));
  }
}
.u-web-date-panel-weeks {
  display: grid;
  grid-template-columns: repeat(7, calc((@u-web-date-picker-width / 7 - 3px) * 1.06));
  padding: 2px 4px;
  color: var(--color-neutral-6);
  font-size: 12px;
  text-align: center;
  margin-bottom: 6px;
}
.u-web-date-panel-grid {
  display: grid;
  grid-template-columns: repeat(7, calc(@u-web-date-picker-width / 7 - 3px));
  grid-auto-rows: calc(@u-web-date-picker-width / 7 - 6px);
  gap: 2px 0;
  padding: 0 6px 6px;
}
.u-web-date-panel-cell {
  position: relative;
  border-radius: 6px;
  padding: 2px 4px;
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &:hover {
    background-color: var(--u-bg-color-3-hover);
  }
  &.other {
    color: var(--color-neutral-6);
  }
  &.today {
    background-color: rgba(var(--blue-4), 0.2);
    color: rgb(var(--blue-7));
  }
  &.selected {
    background: rgba(var(--blue-6));
    color: #ffffff;
  }
  .day {
    font-size: 12px;
  }
  .lunar {
    font-size: 8px;
    padding-top: 1px;
    color: #c3c3c3;
  }
  .corner {
    position: absolute;
    right: 4px;
    top: 4px;
  }
  .tag {
    position: absolute;
    top: -6px;
    right: -6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 11px;
    height: 11px;
    border-radius: 50%;
    font-size: 7px;
    color: #fff;
  }
  // 休息
  .tag.rest {
    background: #00b578;
  }
  // 调休
  .tag.work {
    background: #ff4d4f;
  }
  &.inrange {
    background-color: rgba(0, 137, 255, 0.12);
    border-radius: 0;
    &.other {
      color: var(--color-neutral-6);
      background-color: transparent;
    }
  }
  &.start,
  &.end {
    background-color: rgba(var(--blue-6));
    color: #fff;
    &.other {
      color: var(--color-neutral-6);
      background-color: transparent;
    }
  }

  &.start {
    border-radius: 10px 0 0 10px;
  }

  &.end {
    border-radius: 0 10px 10px 0;
  }
  &.start.end {
    border-radius: 10px;
  }
}

// 月份样式
.u-web-date-panel-month-grid {
  display: grid;
  grid-template-columns: repeat(4, 23%);
  gap: 6px;
  grid-auto-rows: 56px;
  padding: 4px;
}
.u-web-date-panel-month-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
  cursor: pointer;
  .text {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 36px;
    border-radius: 10px;
    &:hover {
      background-color: var(--u-bg-color-3-hover);
    }
  }
  &.current > .text {
    background-color: rgba(var(--blue-4), 0.2);
  }
  &.active > .text {
    background: rgb(var(--blue-6));
    color: #fff;
  }
}
