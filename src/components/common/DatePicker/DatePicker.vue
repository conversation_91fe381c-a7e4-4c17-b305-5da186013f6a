<script setup lang="ts">
import DatePickerPanel from './DatePickerPanel.vue';
const modelValue = defineModel<string>('modelValue');
</script>

<template>
  <div>
    <t-popup overlay-class-name="u-popup-no-style u-date-picker-content" trigger="click">
      <slot name="trigger" v-bind="{ modelValue }">
        <t-input v-model:model-value="modelValue" size="small"></t-input>
      </slot>
      <template #content>
        <DatePickerPanel v-model:modal-value="modelValue" size="small" />
      </template>
    </t-popup>
  </div>
</template>

<style lang="less">
.u-popup-no-style {
  .t-popup__content {
    line-height: unset;
    box-shadow: unset;
  }
}
.u-date-picker-content {
  border-radius: 10px;
  .t-popup__content {
    line-height: 9px;
    box-shadow: rgba(0, 0, 0, 0.08) 0px 4px 12px;
  }
}
</style>
